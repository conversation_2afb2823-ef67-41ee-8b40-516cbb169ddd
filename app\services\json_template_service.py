"""
JSON Template Processing Service
Xử lý slide generation với JSON template từ frontend thay vì Google Slides
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import get_textbook_retrieval_service

logger = logging.getLogger(__name__)


class JsonTemplateService:
    """Service xử lý JSON template từ frontend"""
    
    def __init__(self):
        self.llm_service = get_llm_service()
        self.textbook_service = get_textbook_retrieval_service()

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        return (
            self.llm_service and self.llm_service.is_available() and
            self.textbook_service is not None
        )
    
    async def process_json_template(
        self,
        lesson_id: str,
        template_json: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Xử lý JSON template với nội dung bài học
        
        Args:
            lesson_id: ID của bài học
            template_json: JSON template từ frontend
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            Dict chứa template đã được xử lý
        """
        try:
            logger.info(f"🔄 Processing JSON template for lesson: {lesson_id}")
            logger.info(f"🔍 Template JSON type: {type(template_json)}")
            logger.info(f"🔍 Config prompt: {config_prompt}")

            # Bước 1: Lấy nội dung bài học
            lesson_content = await self._get_lesson_content(lesson_id)
            logger.info(f"🔍 Lesson content result type: {type(lesson_content)}")
            logger.info(f"🔍 Lesson content keys: {list(lesson_content.keys()) if isinstance(lesson_content, dict) else 'Not a dict'}")

            if not lesson_content.get("success", False):
                error_msg = lesson_content.get("error", "Unknown error in lesson content")
                raise Exception(error_msg)

            # Bước 2: Phân tích template và detect placeholders
            try:
                analyzed_template = self._analyze_json_template(template_json)
                logger.info(f"📊 Analyzed template: {len(analyzed_template['slides'])} slides")
            except Exception as e:
                raise Exception(f"Failed to analyze template: {str(e)}")

            # Bước 3: Sinh nội dung với LLM
            presentation_content = await self._generate_presentation_content(
                lesson_content.get("content", ""),
                analyzed_template,
                config_prompt
            )
            logger.info(f"🔍 Presentation content result type: {type(presentation_content)}")
            logger.info(f"🔍 Presentation content keys: {list(presentation_content.keys()) if isinstance(presentation_content, dict) else 'Not a dict'}")

            if not presentation_content.get("success", False):
                error_msg = presentation_content.get("error", "Unknown error in presentation content")
                raise Exception(error_msg)

            # Bước 4: Map nội dung vào template
            try:
                processed_template = await self._map_content_to_json_template(
                    presentation_content.get("content", ""),
                    template_json,
                    analyzed_template
                )
            except Exception as e:
                raise Exception(f"Failed to map content to template: {str(e)}")

            # Trả về trực tiếp nội dung processed_template
            return processed_template
            
        except Exception as e:
            logger.error(f"❌ Error processing JSON template: {e}")
            # Trả về empty template khi có lỗi
            return {
                "version": "1.0",
                "createdAt": datetime.now().isoformat(),
                "slideFormat": "16:9",
                "slides": [],
                "error": f"Failed to process JSON template: {str(e)}"
            }
    
    async def _get_lesson_content(self, lesson_id: str) -> Dict[str, Any]:
        """Lấy nội dung bài học từ TextbookRetrievalService"""
        try:
            logger.info(f"📚 Getting lesson content for: {lesson_id}")

            # Sử dụng TextbookRetrievalService để lấy lesson content
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)

            logger.info(f"🔍 Lesson result keys: {list(lesson_result.keys())}")

            # Extract lesson content từ result
            lesson_content = lesson_result.get("lesson_content", "")

            if not lesson_content or not lesson_content.strip():
                logger.error(f"❌ No lesson content found for lesson_id: {lesson_id}")
                return {
                    "success": False,
                    "error": f"Empty lesson content for lesson_id: {lesson_id}"
                }

            logger.info(f"✅ Retrieved lesson content: {len(lesson_content)} characters")
            logger.info(f"📋 Additional info - Book ID: {lesson_result.get('book_id')}, Total chunks: {lesson_result.get('total_chunks')}")

            return {
                "success": True,
                "content": lesson_content.strip(),
                "book_id": lesson_result.get("book_id"),
                "total_chunks": lesson_result.get("total_chunks"),
                "content_length": lesson_result.get("content_length")
            }

        except Exception as e:
            logger.error(f"❌ Error getting lesson content: {e}")
            return {
                "success": False,
                "error": f"Failed to get lesson content: {str(e)}"
            }
    
    def _analyze_json_template(self, template_json: Dict[str, Any]) -> Dict[str, Any]:
        """Phân tích JSON template và detect placeholders (theo logic cũ)"""
        try:
            logger.info("🔍 Analyzing JSON template structure...")
            logger.info(f"🔍 Template JSON type: {type(template_json)}")
            logger.info(f"🔍 Template JSON keys: {list(template_json.keys()) if isinstance(template_json, dict) else 'Not a dict'}")

            slides = template_json.get("slides", [])
            analyzed_slides = []

            # Placeholder patterns để detect
            placeholder_patterns = {
                "LessonName": r"LessonName\s+(\d+)",
                "LessonDescription": r"LessonDescription\s+(\d+)",
                "CreatedDate": r"CreatedDate\s+(\d+)",
                "TitleName": r"TitleName\s+(\d+)",
                "TitleContent": r"TitleContent\s+(\d+)",
                "SubtitleName": r"SubtitleName\s+(\d+)",
                "SubtitleContent": r"SubtitleContent\s+(\d+)",
                "ImageName": r"ImageName\s+(\d+)",
                "ImageContent": r"ImageContent\s+(\d+)"
            }

            for slide in slides:
                analyzed_elements = []
                placeholder_counts = {}

                # Phân tích elements
                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    # Detect placeholder type từ text
                    placeholder_result = self._detect_placeholder_type_from_text(text, placeholder_patterns)

                    if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                        placeholder_type, max_length = placeholder_result

                        logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                        # Đếm số lượng placeholder types
                        placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                        # Tạo analyzed element với thông tin đầy đủ
                        analyzed_element = {
                            "objectId": element.get("id"),
                            "text": None,  # LLM sẽ insert nội dung sau
                            "Type": placeholder_type,
                            "max_length": max_length,
                            "original_element": element  # Giữ thông tin gốc để mapping
                        }

                        analyzed_elements.append(analyzed_element)
                    else:
                        # Bỏ qua text không phải placeholder format
                        logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                        continue

                # Tạo description cho slide dựa trên placeholder counts (như luồng cũ)
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("id"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts,  # For logic selection
                    "original_slide": slide  # Giữ thông tin gốc
                }

                analyzed_slides.append(analyzed_slide)

            result = {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "slideFormat": template_json.get("slideFormat", "16:9"),
                "version": template_json.get("version", "1.0")
            }

            logger.info(f"✅ Template analysis complete: {len(analyzed_slides)} slides analyzed")
            return result

        except Exception as e:
            logger.error(f"❌ Error analyzing JSON template: {e}")
            raise
    
    async def _generate_presentation_content(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sinh nội dung presentation với LLM"""
        try:
            logger.info("🤖 Generating presentation content with LLM...")
            
            # Tạo prompt cho LLM
            prompt = self._create_llm_prompt(lesson_content, analyzed_template, config_prompt)
            
            # Gọi LLM
            llm_response = await self.llm_service.generate_content(
                prompt=prompt,
                max_tokens=40000,
                temperature=0.2
            )
            
            if not llm_response.get("success", False):
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_response.get('error', 'Unknown error')}"
                }

            content = llm_response.get("text", "")  # LLMService trả về "text" chứ không phải "content"
            logger.info(f"✅ LLM content generated: {len(content)} characters")

            # Debug: Log first 500 chars of LLM content
            logger.info(f"🔍 LLM content preview: {content[:500]}...")

            return {
                "success": True,
                "content": content
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating presentation content: {e}")
            return {
                "success": False,
                "error": f"Failed to generate content: {str(e)}"
            }
    
    def _create_llm_prompt(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """Tạo prompt cho LLM theo format của luồng cũ (chi tiết và chính xác)"""

        # Tạo slide descriptions từ analyzed template
        slide_descriptions = []
        for i, slide in enumerate(analyzed_template["slides"], 1):
            description = slide.get("description", "Slide không xác định")
            slide_descriptions.append(f"SLIDE {i}: {description}")

        # Sử dụng default config từ luồng cũ
        default_config = """
Bạn là chuyên gia thiết kế nội dung thuyết trình giáo dục. Nhiệm vụ của bạn là phân tích nội dung bài học và tạo ra nội dung thuyết trình.
NGUYÊN TẮC THIẾT KẾ:
1. PHÂN TÍCH TOÀN DIỆN - Hiểu rõ nội dung bài học và chia thành các phần logic
2. CẤU TRÚC RÕ RÀNG - Từ tổng quan đến chi tiết, có thứ tự logic
3. NỘI DUNG PHONG PHÚ VÀ CHI TIẾT - Tạo ít nhất 10-12 slides với nội dung đầy đủ
4. ANNOTATION CHÍNH XÁC - Đánh dấu rõ ràng các placeholder type
5. KÝ HIỆU KHOA HỌC CHÍNH XÁC - Sử dụng Unicode cho công thức
6. SLIDE SUMMARIES CHI TIẾT - Ghi rõ số lượng từng placeholder type
YÊU CẦU ANNOTATION:
- PHẢI có annotation bằng #*(PlaceholderType)*# chỉ rõ placeholder type.
- Placeholder types hỗ trợ: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, ImageName, ImageContent
- TẠM THỜI KHÔNG SỬ DỤNG BulletItem - chỉ dùng 9 placeholder types trên
- Annotation phải chính xác và nhất quán
- CẦN có slide summaries với SỐ LƯỢNG RÕ RÀNG để hỗ trợ chọn slide template phù hợp
"""

        # Sử dụng config_prompt nếu có, nếu không thì dùng default_config
        final_config = config_prompt if config_prompt else default_config

        prompt = f"""
{final_config}

NỘI DUNG BÀI HỌC:
{lesson_content}

TEMPLATE STRUCTURE AVAILABLE:
{chr(10).join(slide_descriptions)}

HƯỚNG DẪN TẠO PRESENTATION CONTENT VỚI ANNOTATION:
1. PHÂN TÍCH BÀI HỌC:
   - Xác định chủ đề chính và các chủ đề phụ
   - Chia nội dung thành các phần logic (slides)
   - Mỗi phần có nội dung đầy đủ, chi tiết
   - Xác định thông tin quan trọng cần nhấn mạnh
   - Tránh lược bỏ các thông tin quan trọng trong nội dung bài học được cung cấp
2. TẠO NỘI DUNG VỚI ANNOTATION:
   - PHẢI có annotation #*(PlaceholderType)*# ngay sau
   - Ví dụ: "Bài 1: Cấu hình phân tử #*(LessonName)*#"
   - Ví dụ: "Bài này cho chúng ta biết được cấu hình... #*(LessonDescription)*#"
   - Ví dụ: "Ngày thuyết trình: 18-07-2025 #*(CreatedDate)*#"
   - TẠM THỜI KHÔNG dùng BulletItem - chỉ dùng 9 placeholder types còn lại
3. HIỂU RÕ CẤU TRÚC PHÂN CẤP VÀ NHÓM NỘI DUNG:
   - TitleName: Tên mục lớn (tên nội dung chính của slide đó) - CHỈ LÀ TIÊU ĐỀ
   - TitleContent: Tất cả nội dung giải thích thuộc mục lớn đó - NHÓM TẤT CẢ NỘI DUNG CHUNG
   - SubtitleName: Tên mục nhỏ bên trong mục lớn - CHỈ LÀ TIÊU ĐỀ CON
   - SubtitleContent: Tất cả nội dung giải thích thuộc mục nhỏ (SubtitleName) đó - NHÓM TẤT CẢ NỘI DUNG CON CHUNG
4. Ví dụ CHI TIẾT VỚI CẤU TRÚC PHÂN CẤP RÕ RÀNG VÀ NHÓM NỘI DUNG:
SLIDE 1 - GIỚI THIỆU:
[Tên bài học] #*(LessonName)*#
[Tóm tắt ngắn gọn về bài học] #*(LessonDescription)*#
Ngày thuyết trình: 18-07-2025 #*(CreatedDate)*#
=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================
SLIDE 2 - MỤC LỚN VỚI NỘI DUNG TỔNG QUÁT:
[Tên mục lớn] #*(TitleName)*#
[Tất cả nội dung tổng quát giải thích về mục lớn này, khái niệm chung, định nghĩa. Nếu có nhiều đoạn thì gộp tất cả thành một khối nội dung chung] #*(TitleContent)*#
=== SLIDE 2 SUMMARY ===
Placeholders: 1xTitleName, 1xTitleContent
===========================
SLIDE 3 - CHI TIẾT CÁC MỤC NHỎ TRONG MỤC LỚN:
[Tên mục lớn khác] #*(TitleName)*#
[Tên mục nhỏ thứ nhất] #*(SubtitleName)*#
[Tất cả nội dung chi tiết của mục nhỏ thứ Nhất được gộp chung thành một khối nội dung] #*(SubtitleContent)*#
[Tên mục nhỏ thứ hai] #*(SubtitleName)*#
[Tất cả nội dung chi tiết của mục nhỏ thứ HAI được gộp chung thành một khối nội dung] #*(SubtitleContent)*#
=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 2xSubtitleName, 2xSubtitleContent
===========================
... (tiếp tục với các slide khác tùy theo nội dung bài học)
5. QUY TẮC ANNOTATION VÀ NHÓM NỘI DUNG:
   - LUÔN có annotation #*(PlaceholderType)*# sau mỗi câu/tiêu đề
   - Sử dụng đúng placeholder types: LessonName, LessonDescription, CreatedDate, TitleName, TitleContent, SubtitleName, SubtitleContent, ImageName, ImageContent
   - Annotation phải nhất quán và chính xác
   - Nội dung phải phù hợp với placeholder type
   - QUAN TRỌNG: Mỗi TitleName có thể có nhiều TitleContent thì tất cả TitleContent đều chung 1 TitleContent
   - QUAN TRỌNG: Mỗi SubtitleName có thể có nhiều SubtitleContent thì tất cả SubtitleContent đều chung 1 SubtitleContent
   VÍ DỤ : "Nguyên tố Hydro (H) có tính chất đặc biệt. #*(TitleContent)*#"
   VÍ DỤ CẤU TRÚC ĐÚNG VỚI NHÓM NỘI DUNG:
   Slide 1:
   Khái niệm nguyên tố #*(TitleName)*# ← Đây là tên mục lớn
   Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton. Mỗi nguyên tố có tính chất riêng biệt và được xác định bởi số hiệu nguyên tử. Các nguyên tố được sắp xếp trong bảng tuần hoàn theo thứ tự tăng dần của số hiệu nguyên tử. #*(TitleContent)*# ← Tất cả nội dung mục lớn gộp chung
   Slide 2:
   Đặc điểm của nguyên tố #*(TitleName)*# ← Đây là tên mục lớn khác
    Định nghĩa #*(SubtitleName)*# ← Đây là tên mục nhỏ trong mục lớn
    Nguyên tố được định nghĩa là những chất không thể phân tách thành những chất đơn giản hơn bằng phương pháp hóa học thông thường. #*(SubtitleContent)*# ← Tất cả nội dung các mục nhỏ gộp chung
    Tính chất #*(SubtitleName)*# ← Đây là tên mục nhỏ khác
   Các tính chất của nguyên tố bao gồm tính chất vật lý như màu sắc, trạng thái và tính chất hóa học như khả năng phản ứng. #*(SubtitleContent)*# ← Tất cả nội dung các mục nhỏ gộp chung
6. SLIDE SUMMARIES:
   Cuối mỗi phần logic của presentation, thêm slide summary với SỐ LƯỢNG RÕ RÀNG:
   === SLIDE [Số] SUMMARY ===
   Placeholders: [Số lượng]x[PlaceholderType], [Số lượng]x[PlaceholderType], ...
   Ví dụ: 1xLessonName, 1xLessonDescription, 1xCreatedDate, 2xTitleName, 3xTitleContent
   ===========================
YÊU CẦU OUTPUT:
Tạo nội dung thuyết trình TEXT THUẦN TÚY với annotation rõ ràng, theo đúng format trên.
BẮT BUỘC có slide summaries để hỗ trợ việc chọn slide template phù hợp.
VÍ DỤ MINH HỌA CẤU TRÚC ĐÚNG VỚI NHÓM NỘI DUNG:
SLIDE 1: (Slide này là bắt buộc và luôn có)
Cấu hình electron #*(LessonName)*#
Bài này cho chúng ta biết được cấu hình electron trong nguyên tử và phân tử #*(LessonDescription)*#
Ngày thuyết trình: 18-07-2025 #*(CreatedDate)*#
=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================
SLIDE 2:
Khái niệm cấu hình electron #*(TitleName)*#
Cấu hình electron là cách sắp xếp các electron trong các orbital của nguyên tử. Cấu hình này quyết định tính chất hóa học của nguyên tố và khả năng tạo liên kết. Việc hiểu rõ cấu hình electron giúp dự đoán tính chất và hành vi của các nguyên tố trong phản ứng hóa học. #*(TitleContent)*#
=== SLIDE 2 SUMMARY ===
Placeholders: 1xTitleName, 1xTitleContent
===========================
SLIDE 3:
Các quy tắc sắp xếp electron #*(TitleName)*#
 Quy tắc Aufbau #*(SubtitleName)*#
  Electron điền vào orbital có mức năng lượng thấp trước, sau đó mới điền vào orbital có mức năng lượng cao hơn theo quy tắc Aufbau. #*(SubtitleContent)*#
 Nguyên lý Pauli #*(SubtitleName)*#
  Mỗi orbital chứa tối đa 2 electron và chúng phải có spin ngược chiều nhau theo nguyên lý Pauli. Các quy tắc này đảm bảo cấu hình electron ổn định nhất. #*(SubtitleContent)*#
=== SLIDE 3 SUMMARY ===
Placeholders: 1xTitleName, 2xSubtitleName, 2xSubtitleContent
===========================
SLIDE 4:
Hình ảnh minh họa: Sơ đồ cấu hình electron #*(ImageName)*#
Sơ đồ thể hiện cách electron được sắp xếp trong các orbital 1s, 2s, 2p theo thứ tự năng lượng tăng dần #*(ImageContent)*#
=== SLIDE 4 SUMMARY ===
Placeholders: 1xImageName, 1xImageContent
===========================
QUY TẮC VIẾT VỚI NHÓM NỘI DUNG:
- LUÔN có annotation #*(PlaceholderType)*# sau mỗi nội dung
- Nội dung đầy đủ, chi tiết. Không được bỏ xót bất kì kiến thức nào trong bài học
- TẠMTHỜI KHÔNG sử dụng BulletItem - chỉ dùng 9 placeholder types còn lại
- PHÂN BIỆT RÕ RÀNG CẤU TRÚC PHÂN CẤP VÀ NHÓM NỘI DUNG:
  * TitleName: CHỈ là tiêu đề mục lớn (Tên nội dung chính của slide đó)
  * TitleContent: TẤT CẢ nội dung giải thích của mục lớn được gộp chung thành 1 khối
  * SubtitleName: CHỈ là tiêu đề mục nhỏ bên trong mục lớn
  * SubtitleContent: TẤT CẢ nội dung giải thích của từng mục nhỏ được gộp chung thành 1 khối
- Ký hiệu khoa học chính xác: H₂O, CO₂, x², √x, π, α, β
- Logic trình bày từ tổng quan đến chi tiết
- Sử dụng ngày hiện tại cho CreatedDate
"""

        return prompt

    def _detect_placeholder_type_from_text(self, text: str, placeholder_patterns: Dict[str, str]) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName max_length"

        Args:
            text: Text từ element
            placeholder_patterns: Dictionary của patterns

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            for placeholder_type, pattern in placeholder_patterns.items():
                match = re.search(pattern, text)
                if match:
                    max_length = int(match.group(1))
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts (từ luồng cũ)

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _map_content_to_json_template(
        self,
        llm_content: str,
        original_template: Dict[str, Any],
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map nội dung LLM vào JSON template theo logic của luồng cũ với intelligent slide selection"""
        try:
            logger.info("🔧 Mapping LLM content to JSON template with intelligent slide selection...")

            # Parse LLM content với slide summaries
            parsed_data = self._parse_llm_content(llm_content)
            slide_summaries = parsed_data.get("_slide_summaries", [])

            if not slide_summaries:
                logger.error("❌ No slide summaries found in LLM content")
                raise ValueError("No slide summaries found - cannot perform intelligent slide selection")

            # Create processed template copy
            processed_template = {
                "version": original_template.get("version", "1.0"),
                "createdAt": datetime.now().isoformat(),
                "slideFormat": original_template.get("slideFormat", "16:9"),
                "slides": []
            }

            # Content index để track việc sử dụng content (như luồng cũ)
            content_index = {
                "LessonName": 0,
                "LessonDescription": 0,
                "CreatedDate": 0,
                "TitleName": 0,
                "TitleContent": 0,
                "SubtitleName": 0,
                "SubtitleContent": 0,
                "ImageName": 0,
                "ImageContent": 0
            }

            # Track used slides để tránh duplicate
            used_slide_ids = set()
            template_slides = analyzed_template.get("slides", [])

            logger.info(f"� Processing {len(slide_summaries)} slide summaries with intelligent matching...")

            # Process từng slide summary với intelligent template selection
            for i, summary in enumerate(slide_summaries):
                slide_num = i + 1
                required_placeholders = summary.get("placeholders", [])
                required_counts = summary.get("placeholder_counts", {})

                logger.info(f"🔍 Processing slide {slide_num}:")
                logger.info(f"   Required placeholders: {required_placeholders}")
                logger.info(f"   Required counts: {required_counts}")

                # Tìm template phù hợp CHÍNH XÁC (không fallback)
                # Đầu tiên thử tìm template chưa sử dụng
                best_template = self._find_exact_matching_template(
                    required_placeholders,
                    required_counts,
                    template_slides,
                    used_slide_ids
                )

                # Nếu không tìm thấy template chưa sử dụng, cho phép reuse template
                if not best_template:
                    logger.info(f"🔄 No unused template found, trying to reuse existing template...")
                    best_template = self._find_exact_matching_template_with_reuse(
                        required_placeholders,
                        required_counts,
                        template_slides
                    )

                if best_template:
                    template_id = best_template['slideId']
                    is_reused = template_id in used_slide_ids

                    if is_reused:
                        logger.info(f"✅ Found exact matching template (REUSED): {template_id}")
                    else:
                        logger.info(f"✅ Found exact matching template (NEW): {template_id}")

                    # Tạo processed slide từ template
                    processed_slide = await self._create_processed_slide_from_template(
                        best_template,
                        parsed_data,
                        content_index,
                        slide_num,
                        is_reused
                    )

                    if processed_slide:
                        processed_template["slides"].append(processed_slide)
                        # Chỉ thêm vào used_slide_ids nếu chưa được sử dụng
                        if not is_reused:
                            used_slide_ids.add(template_id)
                        logger.info(f"✅ Successfully processed slide {slide_num} ({'reused' if is_reused else 'new'})")
                    else:
                        logger.error(f"❌ Failed to create processed slide {slide_num} - SKIPPING")
                        # Không fallback - skip slide này
                        continue
                else:
                    logger.error(f"❌ No exact matching template found for slide {slide_num} - SKIPPING")
                    # Không fallback - skip slide này
                    continue

            logger.info(f"✅ Template processing complete: {len(processed_template['slides'])} slides created")
            return processed_template

        except Exception as e:
            logger.error(f"❌ Error mapping content to template: {e}")
            raise

    def _parse_llm_content(self, llm_content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Parse nội dung từ LLM theo format của luồng cũ với slide summaries"""
        try:
            logger.info("📝 Parsing LLM content with slide summaries...")

            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Parse content theo annotation format - LLM sinh theo format: "content #*(PlaceholderType)*#"
            valid_placeholders = '|'.join(parsed_data.keys())

            # Tách content theo từng dòng và match từng dòng
            lines = llm_content.split('\n')
            matches = []

            for line in lines:
                # Pattern để match: "content #*(PlaceholderType)*#" trong một dòng
                pattern = rf'(.+?)\s*#\*\(({valid_placeholders})\)\*#'
                line_matches = re.findall(pattern, line, re.IGNORECASE)
                matches.extend(line_matches)

            logger.info(f"🔍 Found {len(matches)} annotation matches")
            logger.info(f"🔍 Pattern used: {pattern}")
            logger.info(f"🔍 Total lines processed: {len(lines)}")

            for content, placeholder_type in matches:
                clean_content = content.strip()
                if clean_content:
                    parsed_data[placeholder_type].append({
                        "content": clean_content,
                        "length": len(clean_content)
                    })
                    logger.info(f"✅ Parsed {placeholder_type}: {clean_content[:50]}...")

            # Parse slide summaries để hiểu cấu trúc (như luồng cũ)
            slide_summaries = []
            summary_pattern = r'=== SLIDE (\d+) SUMMARY ===\s*Placeholders:\s*([^=]+)'
            summary_matches = re.findall(summary_pattern, llm_content, re.IGNORECASE)

            for slide_num_str, placeholder_text in summary_matches:
                slide_num = int(slide_num_str)
                placeholders = []
                placeholder_counts = {}

                # Parse placeholder counts từ text như "1xLessonName, 2xTitleContent"
                for item in placeholder_text.split(','):
                    item = item.strip()
                    if 'x' in item:
                        # Format: "2xTitleName"
                        count_str, placeholder_type = item.split('x', 1)
                        try:
                            count = int(count_str)
                            placeholders.append(placeholder_type.strip())
                            placeholder_counts[placeholder_type.strip()] = count
                        except ValueError:
                            # Fallback nếu không parse được số
                            placeholders.append(item)
                            placeholder_counts[item] = 1
                    else:
                        # Format cũ: "TitleName"
                        placeholders.append(item)
                        placeholder_counts[item] = 1

                slide_summaries.append({
                    "slide_number": slide_num,
                    "placeholders": placeholders,
                    "placeholder_counts": placeholder_counts
                })

            # Log parsed results
            logger.info(f"📋 Parsed {len(slide_summaries)} slide summaries")
            for placeholder_type, items in parsed_data.items():
                if items:
                    logger.info(f"📋 {placeholder_type}: {len(items)} items")

            # Store slide summaries for mapping logic
            parsed_data["_slide_summaries"] = slide_summaries

            return parsed_data

        except Exception as e:
            logger.error(f"❌ Error parsing LLM content: {e}")
            raise

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str,
        max_retries: int = 3
    ) -> str:
        """Xử lý content vượt quá max_length"""
        try:
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content too long for {placeholder_type}: {len(content)} > {max_length}")

            # Retry với LLM để rút gọn
            for attempt in range(max_retries):
                logger.info(f"🔄 Retry {attempt + 1}/{max_retries} to shorten content...")

                shorten_prompt = f"""Hãy rút gọn nội dung sau để không vượt quá {max_length} ký tự, giữ nguyên ý nghĩa chính:

ORIGINAL CONTENT:
{content}

REQUIREMENTS:
- Tối đa {max_length} ký tự
- Giữ nguyên ý nghĩa chính
- Phù hợp với {placeholder_type}

SHORTENED CONTENT:"""

                llm_response = await self.llm_service.generate_content(
                    prompt=shorten_prompt,
                    max_tokens=max_length // 2,
                    temperature=0.2
                )

                if llm_response.get("success", False):
                    shortened_content = llm_response.get("text", "").strip()
                    if len(shortened_content) <= max_length:
                        logger.info(f"✅ Content shortened: {len(shortened_content)} chars")
                        return shortened_content

            # Không sử dụng fallback truncation
            logger.error(f"❌ Failed to shorten content for {placeholder_type} after {max_retries} retries")
            return content  # Trả về content gốc, để frontend xử lý

        except Exception as e:
            logger.error(f"❌ Error handling max_length content: {e}")
            return content  # Trả về content gốc, không truncate

    def _find_exact_matching_template(
        self,
        required_placeholders: List[str],
        required_counts: Dict[str, int],
        template_slides: List[Dict[str, Any]],
        used_slide_ids: set
    ) -> Optional[Dict[str, Any]]:
        """
        Tìm template slide match chính xác với required placeholders và counts
        (Tương tự logic trong luồng cũ, không fallback)

        Args:
            required_placeholders: List placeholder types cần thiết
            required_counts: Dict số lượng từng placeholder type
            template_slides: List các template slides
            used_slide_ids: Set các slide IDs đã sử dụng

        Returns:
            Dict slide template match chính xác hoặc None
        """
        try:
            for slide in template_slides:
                slide_id = slide.get("slideId")

                # Skip used slides
                if slide_id in used_slide_ids:
                    continue

                # Get placeholder types and counts in this slide
                slide_elements = slide.get("elements", [])
                slide_placeholder_counts = {}

                for elem in slide_elements:
                    placeholder_type = elem.get("Type")
                    if placeholder_type:
                        if placeholder_type in slide_placeholder_counts:
                            slide_placeholder_counts[placeholder_type] += 1
                        else:
                            slide_placeholder_counts[placeholder_type] = 1

                # Check for EXACT match: same placeholder types and same counts
                required_set = set(required_placeholders)
                slide_set = set(slide_placeholder_counts.keys())

                if required_set == slide_set:
                    # Check if counts also match exactly
                    counts_match = True
                    for placeholder_type, required_count in required_counts.items():
                        slide_count = slide_placeholder_counts.get(placeholder_type, 0)
                        if slide_count != required_count:
                            counts_match = False
                            break

                    if counts_match:
                        logger.info(f"✅ Found EXACT matching template: {slide_id}")
                        logger.info(f"   Required: {required_counts}")
                        logger.info(f"   Template has: {slide_placeholder_counts}")
                        return slide
                    else:
                        logger.debug(f"❌ Template {slide_id}: placeholder types match but counts differ")
                        logger.debug(f"   Required: {required_counts}")
                        logger.debug(f"   Template has: {slide_placeholder_counts}")
                else:
                    logger.debug(f"❌ Template {slide_id}: placeholder types don't match")
                    logger.debug(f"   Required: {required_set}")
                    logger.debug(f"   Template has: {slide_set}")

            logger.info(f"❌ No EXACT matching template found for: {required_counts}")
            return None

        except Exception as e:
            logger.error(f"Error finding exact matching template: {e}")
            return None

    def _find_exact_matching_template_with_reuse(
        self,
        required_placeholders: List[str],
        required_counts: Dict[str, int],
        template_slides: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """
        Tìm template slide match chính xác với required placeholders (cho phép reuse)
        (Tương tự logic trong luồng cũ)

        Args:
            required_placeholders: List placeholder types cần thiết
            required_counts: Dict số lượng từng placeholder type
            template_slides: List các template slides

        Returns:
            Dict slide template match chính xác hoặc None
        """
        try:
            logger.info(f"🔍 Finding exact matching template with reuse support...")

            for slide in template_slides:
                slide_id = slide.get("slideId")

                # Get placeholder types and counts in this slide
                slide_elements = slide.get("elements", [])
                slide_placeholder_counts = {}

                for elem in slide_elements:
                    placeholder_type = elem.get("Type")
                    if placeholder_type:
                        if placeholder_type in slide_placeholder_counts:
                            slide_placeholder_counts[placeholder_type] += 1
                        else:
                            slide_placeholder_counts[placeholder_type] = 1

                # Check for EXACT match: same placeholder types and same counts
                required_set = set(required_placeholders)
                slide_set = set(slide_placeholder_counts.keys())

                if required_set == slide_set:
                    # Check if counts also match exactly
                    counts_match = True
                    for placeholder_type, required_count in required_counts.items():
                        slide_count = slide_placeholder_counts.get(placeholder_type, 0)
                        if slide_count != required_count:
                            counts_match = False
                            break

                    if counts_match:
                        logger.info(f"✅ Found EXACT matching template (reuse allowed): {slide_id}")
                        logger.info(f"   Required: {required_counts}")
                        logger.info(f"   Template has: {slide_placeholder_counts}")
                        return slide
                    else:
                        logger.debug(f"❌ Template {slide_id}: placeholder types match but counts differ")
                        logger.debug(f"   Required: {required_counts}")
                        logger.debug(f"   Template has: {slide_placeholder_counts}")
                else:
                    logger.debug(f"❌ Template {slide_id}: placeholder types don't match")
                    logger.debug(f"   Required: {required_set}")
                    logger.debug(f"   Template has: {slide_set}")

            logger.info(f"❌ No EXACT matching template found for reuse: {required_counts}")
            return None

        except Exception as e:
            logger.error(f"Error finding exact matching template with reuse: {e}")
            return None

    async def _create_processed_slide_from_template(
        self,
        template_slide: Dict[str, Any],
        parsed_data: Dict[str, List[Dict[str, Any]]],
        content_index: Dict[str, int],
        slide_number: int,
        is_reused: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Tạo processed slide từ template slide với content mapping
        (Tương tự logic trong luồng cũ, không fallback)

        Args:
            template_slide: Template slide để copy
            parsed_data: Parsed content từ LLM
            content_index: Index tracking cho content usage
            slide_number: Số thứ tự slide

        Returns:
            Dict processed slide hoặc None nếu fail
        """
        try:
            template_slide_id = template_slide.get("slideId")
            template_elements = template_slide.get("elements", [])
            original_slide = template_slide.get("original_slide", {})

            # Tạo slideId mới cho processed slide
            if is_reused:
                new_slide_id = f"slide_{slide_number:03d}_reused_from_{template_slide_id}"
                logger.info(f"📄 Creating processed slide (REUSED): {new_slide_id} (from template: {template_slide_id})")
            else:
                new_slide_id = f"slide_{slide_number:03d}_from_{template_slide_id}"
                logger.info(f"📄 Creating processed slide (NEW): {new_slide_id} (from template: {template_slide_id})")

            processed_slide = {
                "id": new_slide_id,
                "title": original_slide.get("title", ""),
                "elements": [],
                "isVisible": original_slide.get("isVisible", True),
                "background": original_slide.get("background", "#ffffff")
            }

            # Map content vào từng element
            for element in template_elements:
                element_id = element.get("objectId")
                placeholder_type = element.get("Type")
                max_length = element.get("max_length", 1000)
                original_element = element.get("original_element", {})

                # Get content for this placeholder type
                content_list = parsed_data.get(placeholder_type, [])
                current_index = content_index.get(placeholder_type, 0)

                if current_index < len(content_list):
                    content_item = content_list[current_index]
                    raw_content = content_item.get("content", "")

                    try:
                        # Check max_length and handle if needed
                        final_content = await self._handle_max_length_content(
                            raw_content,
                            max_length,
                            placeholder_type
                        )

                        processed_element = {
                            "id": element_id,
                            "type": original_element.get("type", "text"),
                            "x": original_element.get("x", 0),
                            "y": original_element.get("y", 0),
                            "width": original_element.get("width", 0),
                            "height": original_element.get("height", 0),
                            "text": final_content,
                            "style": original_element.get("style", {})
                        }

                        processed_slide["elements"].append(processed_element)

                        # Increment content index
                        content_index[placeholder_type] = current_index + 1

                        logger.debug(f"✅ Mapped {placeholder_type} to {element_id}: {final_content[:50]}...")
                    except Exception as e:
                        logger.error(f"❌ Failed to handle content for {placeholder_type} in slide {slide_number}: {e}")
                        logger.error(f"   Content length: {len(raw_content)}, Max length: {max_length}")
                        logger.error(f"   SKIPPING this slide due to content length issue - NO FALLBACK")
                        return None  # Skip entire slide if any content fails
                else:
                    logger.warning(f"❌ No more content available for {placeholder_type} in slide {slide_number}")
                    return None  # Skip slide if missing content

            logger.info(f"✅ Successfully created processed slide {slide_number} with {len(processed_slide['elements'])} elements")
            return processed_slide

        except Exception as e:
            logger.error(f"❌ Error creating processed slide from template: {e}")
            return None


# Singleton instance
_json_template_service = None

def get_json_template_service() -> JsonTemplateService:
    """Get singleton instance của JsonTemplateService"""
    global _json_template_service
    if _json_template_service is None:
        _json_template_service = JsonTemplateService()
    return _json_template_service
