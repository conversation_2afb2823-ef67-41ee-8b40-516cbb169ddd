"""
JSON Template Processing Service
Xử lý slide generation với JSON template từ frontend thay vì Google Slides
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import get_textbook_retrieval_service

logger = logging.getLogger(__name__)


class JsonTemplateService:
    """Service xử lý JSON template từ frontend"""
    
    def __init__(self):
        self.llm_service = get_llm_service()
        self.textbook_service = get_textbook_retrieval_service()

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        return (
            self.llm_service and self.llm_service.is_available() and
            self.textbook_service is not None
        )
    
    async def process_json_template(
        self,
        lesson_id: str,
        template_json: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Xử lý JSON template với nội dung bài học
        
        Args:
            lesson_id: ID của bài học
            template_json: JSON template từ frontend
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            Dict chứa template đã được xử lý
        """
        try:
            logger.info(f"🔄 Processing JSON template for lesson: {lesson_id}")
            
            # Bước 1: Lấy nội dung bài học
            lesson_content = await self._get_lesson_content(lesson_id)
            if not lesson_content["success"]:
                raise Exception(lesson_content["error"])

            # Bước 2: Phân tích template và detect placeholders
            analyzed_template = self._analyze_json_template(template_json)
            logger.info(f"📊 Analyzed template: {len(analyzed_template['slides'])} slides")

            # Bước 3: Sinh nội dung với LLM
            presentation_content = await self._generate_presentation_content(
                lesson_content["content"],
                analyzed_template,
                config_prompt
            )
            if not presentation_content["success"]:
                raise Exception(presentation_content["error"])

            # Bước 4: Map nội dung vào template
            processed_template = await self._map_content_to_json_template(
                presentation_content["content"],
                template_json,
                analyzed_template
            )

            # Trả về trực tiếp nội dung processed_template
            return processed_template
            
        except Exception as e:
            logger.error(f"❌ Error processing JSON template: {e}")
            # Trả về empty template khi có lỗi
            return {
                "version": "1.0",
                "createdAt": datetime.now().isoformat(),
                "slideFormat": "16:9",
                "slides": [],
                "error": f"Failed to process JSON template: {str(e)}"
            }
    
    async def _get_lesson_content(self, lesson_id: str) -> Dict[str, Any]:
        """Lấy nội dung bài học từ TextbookRetrievalService"""
        try:
            logger.info(f"📚 Getting lesson content for: {lesson_id}")

            # Sử dụng TextbookRetrievalService để lấy lesson content
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)

            logger.info(f"🔍 Lesson result keys: {list(lesson_result.keys())}")

            # Extract lesson content từ result
            lesson_content = lesson_result.get("lesson_content", "")

            if not lesson_content or not lesson_content.strip():
                logger.error(f"❌ No lesson content found for lesson_id: {lesson_id}")
                return {
                    "success": False,
                    "error": f"Empty lesson content for lesson_id: {lesson_id}"
                }

            logger.info(f"✅ Retrieved lesson content: {len(lesson_content)} characters")
            logger.info(f"📋 Additional info - Book ID: {lesson_result.get('book_id')}, Total chunks: {lesson_result.get('total_chunks')}")

            return {
                "success": True,
                "content": lesson_content.strip(),
                "book_id": lesson_result.get("book_id"),
                "total_chunks": lesson_result.get("total_chunks"),
                "content_length": lesson_result.get("content_length")
            }

        except Exception as e:
            logger.error(f"❌ Error getting lesson content: {e}")
            return {
                "success": False,
                "error": f"Failed to get lesson content: {str(e)}"
            }
    
    def _analyze_json_template(self, template_json: Dict[str, Any]) -> Dict[str, Any]:
        """Phân tích JSON template và detect placeholders (theo logic cũ)"""
        try:
            logger.info("🔍 Analyzing JSON template structure...")

            slides = template_json.get("slides", [])
            analyzed_slides = []

            # Placeholder patterns để detect
            placeholder_patterns = {
                "LessonName": r"LessonName\s+(\d+)",
                "LessonDescription": r"LessonDescription\s+(\d+)",
                "CreatedDate": r"CreatedDate\s+(\d+)",
                "TitleName": r"TitleName\s+(\d+)",
                "TitleContent": r"TitleContent\s+(\d+)",
                "SubtitleName": r"SubtitleName\s+(\d+)",
                "SubtitleContent": r"SubtitleContent\s+(\d+)",
                "ImageName": r"ImageName\s+(\d+)",
                "ImageContent": r"ImageContent\s+(\d+)"
            }

            for slide in slides:
                analyzed_elements = []
                placeholder_counts = {}

                # Phân tích elements
                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    # Detect placeholder type từ text
                    placeholder_result = self._detect_placeholder_type_from_text(text, placeholder_patterns)

                    if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                        placeholder_type, max_length = placeholder_result

                        logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                        # Đếm số lượng placeholder types
                        placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                        # Tạo analyzed element với thông tin đầy đủ
                        analyzed_element = {
                            "objectId": element.get("id"),
                            "text": None,  # LLM sẽ insert nội dung sau
                            "Type": placeholder_type,
                            "max_length": max_length,
                            "original_element": element  # Giữ thông tin gốc để mapping
                        }

                        analyzed_elements.append(analyzed_element)
                    else:
                        # Bỏ qua text không phải placeholder format
                        logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                        continue

                # Tạo description cho slide dựa trên placeholder counts (như luồng cũ)
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("id"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts,  # For logic selection
                    "original_slide": slide  # Giữ thông tin gốc
                }

                analyzed_slides.append(analyzed_slide)

            result = {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "slideFormat": template_json.get("slideFormat", "16:9"),
                "version": template_json.get("version", "1.0")
            }

            logger.info(f"✅ Template analysis complete: {len(analyzed_slides)} slides analyzed")
            return result

        except Exception as e:
            logger.error(f"❌ Error analyzing JSON template: {e}")
            raise
    
    async def _generate_presentation_content(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sinh nội dung presentation với LLM"""
        try:
            logger.info("🤖 Generating presentation content with LLM...")
            
            # Tạo prompt cho LLM
            prompt = self._create_llm_prompt(lesson_content, analyzed_template, config_prompt)
            
            # Gọi LLM
            llm_response = await self.llm_service.generate_content(
                prompt=prompt,
                max_tokens=20000,
                temperature=0.1
            )
            
            if not llm_response.get("success", False):
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_response.get('error', 'Unknown error')}"
                }

            content = llm_response.get("text", "")  # LLMService trả về "text" chứ không phải "content"
            logger.info(f"✅ LLM content generated: {len(content)} characters")

            # Debug: Log first 500 chars of LLM content
            logger.info(f"🔍 LLM content preview: {content[:500]}...")

            return {
                "success": True,
                "content": content
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating presentation content: {e}")
            return {
                "success": False,
                "error": f"Failed to generate content: {str(e)}"
            }
    
    def _create_llm_prompt(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """Tạo prompt cho LLM theo format của luồng cũ"""

        # Tạo slide descriptions từ analyzed template
        slide_descriptions = []
        for i, slide in enumerate(analyzed_template["slides"], 1):
            description = slide.get("description", "Slide không xác định")
            slide_descriptions.append(f"SLIDE {i}: {description}")

        prompt = f"""Bạn là chuyên gia tạo nội dung slide giáo dục. Hãy tạo nội dung cho presentation dựa trên bài học sau:

LESSON CONTENT:
{lesson_content}

TEMPLATE STRUCTURE:
{chr(10).join(slide_descriptions)}

PLACEHOLDER TYPES VÀ Ý NGHĨA:
- LessonName: Tên bài học ngắn gọn
- LessonDescription: Mô tả tổng quan về bài học
- CreatedDate: Ngày tạo slide (định dạng dd/mm/yyyy)
- TitleName: Tiêu đề chính của slide (chỉ là tiêu đề)
- TitleContent: Nội dung chi tiết thuộc mục lớn (nhóm tất cả nội dung chung)
- SubtitleName: Tiêu đề phụ bên trong mục lớn (chỉ là tiêu đề con)
- SubtitleContent: Nội dung chi tiết thuộc mục nhỏ (nhóm tất cả nội dung con chung)
- ImageName: Tên/mô tả hình ảnh
- ImageContent: Mô tả chi tiết nội dung hình ảnh

INSTRUCTIONS:
1. Tạo nội dung cho từng placeholder theo format: #*(PlaceholderType)*# [nội dung]
2. Sau mỗi slide, thêm summary line: === SLIDE X SUMMARY === và Placeholders: [danh sách placeholders]
3. Nội dung phải liên quan trực tiếp đến bài học
4. TitleContent và SubtitleContent phải nhóm tất cả nội dung liên quan
5. Sử dụng ngôn ngữ phù hợp với học sinh

VÍ DỤ FORMAT:
SLIDE 1 - GIỚI THIỆU:
Hàm số bậc nhất #*(LessonName)*#
Bài học về hàm số có dạng y = ax + b với a ≠ 0 #*(LessonDescription)*#
18/07/2025 #*(CreatedDate)*#
=== SLIDE 1 SUMMARY ===
Placeholders: 1xLessonName, 1xLessonDescription, 1xCreatedDate
===========================
"""

        if config_prompt:
            prompt += f"\nADDITIONAL REQUIREMENTS:\n{config_prompt}\n"

        prompt += "\nHãy tạo nội dung theo format trên:"

        return prompt

    def _detect_placeholder_type_from_text(self, text: str, placeholder_patterns: Dict[str, str]) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName max_length"

        Args:
            text: Text từ element
            placeholder_patterns: Dictionary của patterns

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            for placeholder_type, pattern in placeholder_patterns.items():
                match = re.search(pattern, text)
                if match:
                    max_length = int(match.group(1))
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts (từ luồng cũ)

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _map_content_to_json_template(
        self,
        llm_content: str,
        original_template: Dict[str, Any],
        analyzed_template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Map nội dung LLM vào JSON template theo logic của luồng cũ với intelligent slide selection"""
        try:
            logger.info("🔧 Mapping LLM content to JSON template with intelligent slide selection...")

            # Parse LLM content với slide summaries
            parsed_data = self._parse_llm_content(llm_content)
            slide_summaries = parsed_data.get("_slide_summaries", [])

            if not slide_summaries:
                logger.error("❌ No slide summaries found in LLM content")
                raise ValueError("No slide summaries found - cannot perform intelligent slide selection")

            # Create processed template copy
            processed_template = {
                "version": original_template.get("version", "1.0"),
                "createdAt": datetime.now().isoformat(),
                "slideFormat": original_template.get("slideFormat", "16:9"),
                "slides": []
            }

            # Content index để track việc sử dụng content (như luồng cũ)
            content_index = {
                "LessonName": 0,
                "LessonDescription": 0,
                "CreatedDate": 0,
                "TitleName": 0,
                "TitleContent": 0,
                "SubtitleName": 0,
                "SubtitleContent": 0,
                "ImageName": 0,
                "ImageContent": 0
            }

            # Track used slides để tránh duplicate
            used_slide_ids = set()
            template_slides = analyzed_template.get("slides", [])

            logger.info(f"� Processing {len(slide_summaries)} slide summaries with intelligent matching...")

            # Process từng slide summary với intelligent template selection
            for i, summary in enumerate(slide_summaries):
                slide_num = i + 1
                required_placeholders = summary.get("placeholders", [])
                required_counts = summary.get("placeholder_counts", {})

                logger.info(f"🔍 Processing slide {slide_num}:")
                logger.info(f"   Required placeholders: {required_placeholders}")
                logger.info(f"   Required counts: {required_counts}")

                # Tìm template phù hợp CHÍNH XÁC (không fallback)
                best_template = self._find_exact_matching_template(
                    required_placeholders,
                    required_counts,
                    template_slides,
                    used_slide_ids
                )

                if best_template:
                    logger.info(f"✅ Found exact matching template: {best_template['slideId']}")

                    # Tạo processed slide từ template
                    processed_slide = await self._create_processed_slide_from_template(
                        best_template,
                        parsed_data,
                        content_index,
                        slide_num
                    )

                    if processed_slide:
                        processed_template["slides"].append(processed_slide)
                        used_slide_ids.add(best_template["slideId"])
                        logger.info(f"✅ Successfully processed slide {slide_num}")
                    else:
                        logger.error(f"❌ Failed to create processed slide {slide_num} - SKIPPING")
                        # Không fallback - skip slide này
                        continue
                else:
                    logger.error(f"❌ No exact matching template found for slide {slide_num} - SKIPPING")
                    # Không fallback - skip slide này
                    continue

            logger.info(f"✅ Template processing complete: {len(processed_template['slides'])} slides created")
            return processed_template

        except Exception as e:
            logger.error(f"❌ Error mapping content to template: {e}")
            raise

    def _parse_llm_content(self, llm_content: str) -> Dict[str, List[Dict[str, Any]]]:
        """Parse nội dung từ LLM theo format của luồng cũ với slide summaries"""
        try:
            logger.info("📝 Parsing LLM content with slide summaries...")

            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Parse content theo annotation format - LLM sinh theo format: "content #*(PlaceholderType)*#"
            valid_placeholders = '|'.join(parsed_data.keys())

            # Tách content theo từng dòng và match từng dòng
            lines = llm_content.split('\n')
            matches = []

            for line in lines:
                # Pattern để match: "content #*(PlaceholderType)*#" trong một dòng
                pattern = rf'(.+?)\s*#\*\(({valid_placeholders})\)\*#'
                line_matches = re.findall(pattern, line, re.IGNORECASE)
                matches.extend(line_matches)

            logger.info(f"🔍 Found {len(matches)} annotation matches")
            logger.info(f"🔍 Pattern used: {pattern}")
            logger.info(f"🔍 Total lines processed: {len(lines)}")

            for content, placeholder_type in matches:
                clean_content = content.strip()
                if clean_content:
                    parsed_data[placeholder_type].append({
                        "content": clean_content,
                        "length": len(clean_content)
                    })
                    logger.info(f"✅ Parsed {placeholder_type}: {clean_content[:50]}...")

            # Parse slide summaries để hiểu cấu trúc (như luồng cũ)
            slide_summaries = []
            summary_pattern = r'=== SLIDE (\d+) SUMMARY ===\s*Placeholders:\s*([^=]+)'
            summary_matches = re.findall(summary_pattern, llm_content, re.IGNORECASE)

            for slide_num_str, placeholder_text in summary_matches:
                slide_num = int(slide_num_str)
                placeholders = []
                placeholder_counts = {}

                # Parse placeholder counts từ text như "1xLessonName, 2xTitleContent"
                for item in placeholder_text.split(','):
                    item = item.strip()
                    if 'x' in item:
                        # Format: "2xTitleName"
                        count_str, placeholder_type = item.split('x', 1)
                        try:
                            count = int(count_str)
                            placeholders.append(placeholder_type.strip())
                            placeholder_counts[placeholder_type.strip()] = count
                        except ValueError:
                            # Fallback nếu không parse được số
                            placeholders.append(item)
                            placeholder_counts[item] = 1
                    else:
                        # Format cũ: "TitleName"
                        placeholders.append(item)
                        placeholder_counts[item] = 1

                slide_summaries.append({
                    "slide_number": slide_num,
                    "placeholders": placeholders,
                    "placeholder_counts": placeholder_counts
                })

            # Log parsed results
            logger.info(f"📋 Parsed {len(slide_summaries)} slide summaries")
            for placeholder_type, items in parsed_data.items():
                if items:
                    logger.info(f"📋 {placeholder_type}: {len(items)} items")

            # Store slide summaries for mapping logic
            parsed_data["_slide_summaries"] = slide_summaries

            return parsed_data

        except Exception as e:
            logger.error(f"❌ Error parsing LLM content: {e}")
            raise

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str,
        max_retries: int = 3
    ) -> str:
        """Xử lý content vượt quá max_length"""
        try:
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content too long for {placeholder_type}: {len(content)} > {max_length}")

            # Retry với LLM để rút gọn
            for attempt in range(max_retries):
                logger.info(f"🔄 Retry {attempt + 1}/{max_retries} to shorten content...")

                shorten_prompt = f"""Hãy rút gọn nội dung sau để không vượt quá {max_length} ký tự, giữ nguyên ý nghĩa chính:

ORIGINAL CONTENT:
{content}

REQUIREMENTS:
- Tối đa {max_length} ký tự
- Giữ nguyên ý nghĩa chính
- Phù hợp với {placeholder_type}

SHORTENED CONTENT:"""

                llm_response = await self.llm_service.generate_content(
                    prompt=shorten_prompt,
                    max_tokens=max_length // 2,
                    temperature=0.2
                )

                if llm_response.get("success", False):
                    shortened_content = llm_response.get("text", "").strip()
                    if len(shortened_content) <= max_length:
                        logger.info(f"✅ Content shortened: {len(shortened_content)} chars")
                        return shortened_content

            # Không sử dụng fallback truncation
            logger.error(f"❌ Failed to shorten content for {placeholder_type} after {max_retries} retries")
            return content  # Trả về content gốc, để frontend xử lý

        except Exception as e:
            logger.error(f"❌ Error handling max_length content: {e}")
            return content  # Trả về content gốc, không truncate

    def _find_exact_matching_template(
        self,
        required_placeholders: List[str],
        required_counts: Dict[str, int],
        template_slides: List[Dict[str, Any]],
        used_slide_ids: set
    ) -> Optional[Dict[str, Any]]:
        """
        Tìm template slide match chính xác với required placeholders và counts
        (Tương tự logic trong luồng cũ, không fallback)

        Args:
            required_placeholders: List placeholder types cần thiết
            required_counts: Dict số lượng từng placeholder type
            template_slides: List các template slides
            used_slide_ids: Set các slide IDs đã sử dụng

        Returns:
            Dict slide template match chính xác hoặc None
        """
        try:
            for slide in template_slides:
                slide_id = slide.get("slideId")

                # Skip used slides
                if slide_id in used_slide_ids:
                    continue

                # Get placeholder types and counts in this slide
                slide_elements = slide.get("elements", [])
                slide_placeholder_counts = {}

                for elem in slide_elements:
                    placeholder_type = elem.get("Type")
                    if placeholder_type:
                        if placeholder_type in slide_placeholder_counts:
                            slide_placeholder_counts[placeholder_type] += 1
                        else:
                            slide_placeholder_counts[placeholder_type] = 1

                # Check for EXACT match: same placeholder types and same counts
                required_set = set(required_placeholders)
                slide_set = set(slide_placeholder_counts.keys())

                if required_set == slide_set:
                    # Check if counts also match exactly
                    counts_match = True
                    for placeholder_type, required_count in required_counts.items():
                        slide_count = slide_placeholder_counts.get(placeholder_type, 0)
                        if slide_count != required_count:
                            counts_match = False
                            break

                    if counts_match:
                        logger.info(f"✅ Found EXACT matching template: {slide_id}")
                        logger.info(f"   Required: {required_counts}")
                        logger.info(f"   Template has: {slide_placeholder_counts}")
                        return slide
                    else:
                        logger.debug(f"❌ Template {slide_id}: placeholder types match but counts differ")
                        logger.debug(f"   Required: {required_counts}")
                        logger.debug(f"   Template has: {slide_placeholder_counts}")
                else:
                    logger.debug(f"❌ Template {slide_id}: placeholder types don't match")
                    logger.debug(f"   Required: {required_set}")
                    logger.debug(f"   Template has: {slide_set}")

            logger.info(f"❌ No EXACT matching template found for: {required_counts}")
            return None

        except Exception as e:
            logger.error(f"Error finding exact matching template: {e}")
            return None

    async def _create_processed_slide_from_template(
        self,
        template_slide: Dict[str, Any],
        parsed_data: Dict[str, List[Dict[str, Any]]],
        content_index: Dict[str, int],
        slide_number: int
    ) -> Optional[Dict[str, Any]]:
        """
        Tạo processed slide từ template slide với content mapping
        (Tương tự logic trong luồng cũ, không fallback)

        Args:
            template_slide: Template slide để copy
            parsed_data: Parsed content từ LLM
            content_index: Index tracking cho content usage
            slide_number: Số thứ tự slide

        Returns:
            Dict processed slide hoặc None nếu fail
        """
        try:
            template_slide_id = template_slide.get("slideId")
            template_elements = template_slide.get("elements", [])
            original_slide = template_slide.get("original_slide", {})

            # Tạo slideId mới cho processed slide
            new_slide_id = f"slide_{slide_number:03d}_from_{template_slide_id}"

            logger.info(f"📄 Creating processed slide: {new_slide_id} (from template: {template_slide_id})")

            processed_slide = {
                "id": new_slide_id,
                "title": original_slide.get("title", ""),
                "elements": [],
                "isVisible": original_slide.get("isVisible", True),
                "background": original_slide.get("background", "#ffffff")
            }

            # Map content vào từng element
            for element in template_elements:
                element_id = element.get("objectId")
                placeholder_type = element.get("Type")
                max_length = element.get("max_length", 1000)
                original_element = element.get("original_element", {})

                # Get content for this placeholder type
                content_list = parsed_data.get(placeholder_type, [])
                current_index = content_index.get(placeholder_type, 0)

                if current_index < len(content_list):
                    content_item = content_list[current_index]
                    raw_content = content_item.get("content", "")

                    try:
                        # Check max_length and handle if needed
                        final_content = await self._handle_max_length_content(
                            raw_content,
                            max_length,
                            placeholder_type
                        )

                        processed_element = {
                            "id": element_id,
                            "type": original_element.get("type", "text"),
                            "x": original_element.get("x", 0),
                            "y": original_element.get("y", 0),
                            "width": original_element.get("width", 0),
                            "height": original_element.get("height", 0),
                            "text": final_content,
                            "style": original_element.get("style", {})
                        }

                        processed_slide["elements"].append(processed_element)

                        # Increment content index
                        content_index[placeholder_type] = current_index + 1

                        logger.debug(f"✅ Mapped {placeholder_type} to {element_id}: {final_content[:50]}...")
                    except Exception as e:
                        logger.error(f"❌ Failed to handle content for {placeholder_type} in slide {slide_number}: {e}")
                        logger.error(f"   Content length: {len(raw_content)}, Max length: {max_length}")
                        logger.error(f"   SKIPPING this slide due to content length issue - NO FALLBACK")
                        return None  # Skip entire slide if any content fails
                else:
                    logger.warning(f"❌ No more content available for {placeholder_type} in slide {slide_number}")
                    return None  # Skip slide if missing content

            logger.info(f"✅ Successfully created processed slide {slide_number} with {len(processed_slide['elements'])} elements")
            return processed_slide

        except Exception as e:
            logger.error(f"❌ Error creating processed slide from template: {e}")
            return None


# Singleton instance
_json_template_service = None

def get_json_template_service() -> JsonTemplateService:
    """Get singleton instance của JsonTemplateService"""
    global _json_template_service
    if _json_template_service is None:
        _json_template_service = JsonTemplateService()
    return _json_template_service
