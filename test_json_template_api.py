"""
Test script cho JSON Template API
Kiểm tra endpoint /api/v1/slides/process-json-template
"""

import requests
import json
from datetime import datetime

# API endpoint
BASE_URL = "http://localhost:8000"
API_ENDPOINT = f"{BASE_URL}/api/v1/slides/process-json-template"

# Sample JSON template (giống như trong token.json)
SAMPLE_TEMPLATE = {
    "version": "1.0",
    "createdAt": "2025-07-17T11:02:46.948Z",
    "slideFormat": "16:9",
    "slides": [
        {
            "id": "p1",
            "title": "Slide 1",
            "elements": [
                {
                    "id": "p1_i1559",
                    "type": "text",
                    "x": 136,
                    "y": 96,
                    "width": 701,
                    "height": 191,
                    "text": "LessonName 60",
                    "style": {
                        "fontSize": 40,
                        "fontFamily": "Arial",
                        "bold": False,
                        "italic": True,
                        "underline": False,
                        "color": "#000000",
                        "backgroundColor": "transparent",
                        "textAlign": "center"
                    }
                },
                {
                    "id": "p1_i1560",
                    "type": "text",
                    "x": 185,
                    "y": 271,
                    "width": 595,
                    "height": 136,
                    "text": "LessonDescription 180",
                    "style": {
                        "fontSize": 14,
                        "fontFamily": "Arial",
                        "bold": False,
                        "italic": False,
                        "underline": False,
                        "color": "#000000",
                        "backgroundColor": "rgb(94, 108, 213)",
                        "textAlign": "center"
                    }
                },
                {
                    "id": "g36ebc629d7b_0_131",
                    "type": "text",
                    "x": 248,
                    "y": 434,
                    "width": 475,
                    "height": 43,
                    "text": "CreatedDate 50",
                    "style": {
                        "fontSize": 12,
                        "fontFamily": "Arial",
                        "bold": True,
                        "italic": False,
                        "underline": False,
                        "color": "#000000",
                        "backgroundColor": "rgb(94, 108, 213)",
                        "textAlign": "center"
                    }
                }
            ],
            "isVisible": True,
            "background": "#ffffff"
        },
        {
            "id": "p5",
            "title": "Slide 2",
            "elements": [
                {
                    "id": "p5_i1647",
                    "type": "text",
                    "x": 73,
                    "y": 148,
                    "width": 602,
                    "height": 91,
                    "text": "TitleName 70",
                    "style": {
                        "fontSize": 19,
                        "fontFamily": "Arial",
                        "bold": False,
                        "italic": False,
                        "underline": False,
                        "color": "#000000",
                        "backgroundColor": "transparent",
                        "textAlign": "left"
                    }
                },
                {
                    "id": "p5_i1649",
                    "type": "text",
                    "x": 73,
                    "y": 239,
                    "width": 685,
                    "height": 227,
                    "text": "TitleContent 350",
                    "style": {
                        "fontSize": 12,
                        "fontFamily": "Arial",
                        "bold": False,
                        "italic": False,
                        "underline": False,
                        "color": "#000000",
                        "backgroundColor": "rgb(65, 77, 175)",
                        "textAlign": "left"
                    }
                }
            ],
            "isVisible": True,
            "background": "#ffffff"
        }
    ]
}

def test_json_template_api():
    """Test JSON template processing API"""
    
    print("🧪 Testing JSON Template API...")
    print(f"📡 Endpoint: {API_ENDPOINT}")
    
    # Prepare request data
    request_data = {
        "lesson_id": "1",
        "template": SAMPLE_TEMPLATE,
        "config_prompt": "Tạo slide về môn Toán lớp 10, chủ đề hàm số bậc nhất"
    }
    
    print(f"📋 Request data:")
    print(f"  - Lesson ID: {request_data['lesson_id']}")
    print(f"  - Template slides: {len(request_data['template']['slides'])}")
    print(f"  - Config prompt: {request_data['config_prompt']}")
    
    try:
        # Send POST request
        print("\n🔄 Sending request...")
        response = requests.post(
            API_ENDPOINT,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"📋 Response data:")
            print(f"  - Success: {result.get('success')}")
            print(f"  - Lesson ID: {result.get('lesson_id')}")
            print(f"  - Slides created: {result.get('slides_created')}")
            
            if result.get('processed_template'):
                processed_slides = result['processed_template'].get('slides', [])
                print(f"  - Processed slides: {len(processed_slides)}")
                
                # Show first slide content as example
                if processed_slides:
                    first_slide = processed_slides[0]
                    print(f"\n📄 First slide example:")
                    print(f"  - ID: {first_slide.get('id')}")
                    print(f"  - Title: {first_slide.get('title')}")
                    print(f"  - Elements: {len(first_slide.get('elements', []))}")
                    
                    # Show first element content
                    elements = first_slide.get('elements', [])
                    if elements:
                        first_element = elements[0]
                        text_content = first_element.get('text', '')
                        print(f"  - First element text: {text_content[:100]}...")
            
            # Save result to file for inspection
            with open('json_template_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Full result saved to: json_template_result.json")
            
        else:
            print("❌ Error!")
            try:
                error_data = response.json()
                print(f"📋 Error details:")
                print(f"  - Error code: {error_data.get('detail', {}).get('error_code')}")
                print(f"  - Error message: {error_data.get('detail', {}).get('error_message')}")
            except:
                print(f"📋 Raw error response: {response.text}")
    
    except requests.exceptions.Timeout:
        print("⏰ Request timeout - API might be processing...")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - Make sure the API server is running")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_health_check():
    """Test health check endpoint"""
    
    print("\n🏥 Testing health check...")
    health_url = f"{BASE_URL}/api/v1/slides/health"
    
    try:
        response = requests.get(health_url, timeout=10)
        print(f"📊 Health status: {response.status_code}")
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check result:")
            print(f"  - Status: {health_data.get('status')}")
            print(f"  - Services: {health_data.get('services')}")
            print(f"  - Available endpoints: {len(health_data.get('available_endpoints', []))}")
        else:
            print(f"❌ Health check failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 JSON Template API Test")
    print("=" * 60)
    
    # Test health check first
    test_health_check()
    
    # Test main API
    test_json_template_api()
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
    print("=" * 60)
