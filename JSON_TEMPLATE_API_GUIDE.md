# JSON Template API Guide

## Tổng quan

API mới `/api/v1/slides/process-json-template` cho phép xử lý slide generation với JSON template từ frontend thay vì sử dụng Google Slides template.

## Workflow mới

```
Frontend JSON Template → LLM Content Generation → Processed JSON Template → Frontend
```

### So sánh với workflow cũ:
- **Cũ**: Google Slides Template → Copy → Analyze → LLM → Update Google Slides → Return Link
- **Mới**: JSON Template → Analyze → LLM → Map Content → Return Processed JSON

## API Endpoint

### POST `/api/v1/slides/process-json-template`

Xử lý JSON template với nội dung bài học từ Qdrant.

#### Request Body

```json
{
  "lesson_id": "string",
  "template": {
    "version": "1.0",
    "createdAt": "2025-07-17T11:02:46.948Z",
    "slideFormat": "16:9",
    "slides": [
      {
        "id": "p1",
        "title": "Slide 1",
        "elements": [
          {
            "id": "p1_i1559",
            "type": "text",
            "x": 136,
            "y": 96,
            "width": 701,
            "height": 191,
            "text": "LessonName 60",
            "style": {
              "fontSize": 40,
              "fontFamily": "Arial",
              "bold": false,
              "italic": true,
              "underline": false,
              "color": "#000000",
              "backgroundColor": "transparent",
              "textAlign": "center"
            }
          }
        ],
        "isVisible": true,
        "background": "#ffffff"
      }
    ]
  },
  "config_prompt": "string (optional)"
}
```

#### Response

```json
{
  "success": true,
  "lesson_id": "string",
  "processed_template": {
    "version": "1.0",
    "createdAt": "2025-07-18T...",
    "slideFormat": "16:9",
    "slides": [
      {
        "id": "p1",
        "title": "Slide 1",
        "elements": [
          {
            "id": "p1_i1559",
            "type": "text",
            "x": 136,
            "y": 96,
            "width": 701,
            "height": 191,
            "text": "Hàm số bậc nhất",
            "style": { ... }
          }
        ],
        "isVisible": true,
        "background": "#ffffff"
      }
    ]
  },
  "slides_created": 2,
  "error": null,
  "created_at": "2025-07-18T..."
}
```

## Placeholder Detection

API tự động detect các placeholder trong text elements dựa trên pattern:

### Supported Placeholders

| Placeholder | Pattern | Mô tả |
|-------------|---------|-------|
| `LessonName` | `LessonName 60` | Tên bài học (max 60 chars) |
| `LessonDescription` | `LessonDescription 180` | Mô tả bài học (max 180 chars) |
| `CreatedDate` | `CreatedDate 50` | Ngày tạo (max 50 chars) |
| `TitleName` | `TitleName 70` | Tiêu đề slide (max 70 chars) |
| `TitleContent` | `TitleContent 350` | Nội dung chính (max 350 chars) |
| `SubtitleName` | `SubtitleName 100` | Tiêu đề phụ (max 100 chars) |
| `SubtitleContent` | `SubtitleContent 200` | Nội dung phụ (max 200 chars) |
| `ImageName` | `ImageName 150` | Tên hình ảnh (max 150 chars) |
| `ImageContent` | `ImageContent 300` | Mô tả hình ảnh (max 300 chars) |

### Ví dụ Template Element

```json
{
  "id": "element_1",
  "type": "text",
  "x": 100,
  "y": 100,
  "width": 500,
  "height": 100,
  "text": "TitleName 70",
  "style": { ... }
}
```

Sẽ được xử lý thành:

```json
{
  "id": "element_1",
  "type": "text",
  "x": 100,
  "y": 100,
  "width": 500,
  "height": 100,
  "text": "Hàm số bậc nhất và đồ thị",
  "style": { ... }
}
```

## LLM Content Generation

### Prompt Format

LLM sẽ sinh nội dung theo format annotation:

```
#*(PlaceholderType)*# [nội dung]
```

### Ví dụ LLM Output

```
#*(LessonName)*# Hàm số bậc nhất
#*(LessonDescription)*# Bài học về hàm số bậc nhất y = ax + b, cách vẽ đồ thị và ứng dụng trong thực tế
#*(CreatedDate)*# 18/07/2025
#*(TitleName)*# Định nghĩa hàm số bậc nhất
#*(TitleContent)*# Hàm số bậc nhất là hàm số có dạng y = ax + b, trong đó a ≠ 0. Đây là một trong những hàm số cơ bản nhất trong toán học.
```

## Content Mapping Process

1. **Parse LLM Content**: Tách nội dung theo annotation format
2. **Content Index Tracking**: Theo dõi việc sử dụng content cho từng placeholder type
3. **Sequential Mapping**: Map content theo thứ tự xuất hiện trong template
4. **Max Length Handling**: Tự động rút gọn nội dung nếu vượt quá giới hạn

### Content Reuse Logic

- `TitleContent` được share giữa các `TitleName` instances
- `SubtitleContent` được share giữa các `SubtitleName` instances
- Mỗi placeholder type có content index riêng

## Error Handling

### Common Error Codes

- `LESSON_NOT_FOUND`: Không tìm thấy nội dung bài học
- `LLM_GENERATION_FAILED`: LLM không thể sinh nội dung
- `UNKNOWN_ERROR`: Lỗi không xác định

### Error Response Format

```json
{
  "success": false,
  "error": "Error message",
  "error_code": "ERROR_CODE"
}
```

## Testing

Sử dụng script `test_json_template_api.py` để test API:

```bash
python test_json_template_api.py
```

## Integration với Frontend

### 1. Chuẩn bị Template JSON
Frontend tạo template JSON với placeholders theo format đã định.

### 2. Gọi API
```javascript
const response = await fetch('/api/v1/slides/process-json-template', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    lesson_id: 'lesson_123',
    template: templateJson,
    config_prompt: 'Tạo slide sinh động cho học sinh lớp 10'
  })
});

const result = await response.json();
```

### 3. Sử dụng Processed Template
```javascript
if (result.success) {
  const processedTemplate = result.processed_template;
  // Render slides với nội dung đã được xử lý
  renderSlides(processedTemplate.slides);
}
```

## Lưu ý quan trọng

1. **Không thay đổi API cũ**: API cũ vẫn hoạt động bình thường
2. **Lesson Content**: Cần đảm bảo lesson_id tồn tại trong Qdrant
3. **Template Format**: Tuân thủ đúng format JSON template
4. **Placeholder Pattern**: Sử dụng đúng pattern để detect placeholders
5. **Max Length**: Nội dung sẽ được tự động rút gọn nếu vượt quá giới hạn

## Health Check

Kiểm tra trạng thái service:

```bash
GET /api/v1/slides/health
```

Response sẽ bao gồm endpoint mới trong `available_endpoints`.
